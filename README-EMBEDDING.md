# 阿里云百炼嵌入服务集成

本文档介绍如何在图片搜索系统中集成阿里云百炼嵌入模型，实现真实的文本向量化功能。

## 架构设计

### 分层架构

```
领域层 (Domain Layer)
├── EmbeddingService (接口)
└── VectorSearchService (使用嵌入服务)

防腐层 (Anti-Corruption Layer)
└── AliyunEmbeddingServiceImpl (实现EmbeddingService接口)

基础设施层 (Infrastructure Layer)
├── AliyunEmbeddingClient (HTTP客户端)
├── EmbeddingRequest/Response (DTO)
└── AliyunEmbeddingConfig (配置)
```

### 核心组件

1. **EmbeddingService**: 领域层接口，定义文本向量化的标准操作
2. **AliyunEmbeddingServiceImpl**: 防腐层实现，封装阿里云API调用
3. **AliyunEmbeddingClient**: 基础设施层HTTP客户端，处理网络通信
4. **AliyunEmbeddingConfig**: 配置类，管理API密钥、端点等参数

## 配置说明

### 1. 环境变量配置

设置阿里云API密钥：

```bash
# Windows
set DASHSCOPE_API_KEY=sk-5713eb161252416daa0e89d031b02436

# Linux/Mac
export DASHSCOPE_API_KEY=sk-5713eb161252416daa0e89d031b02436
```

### 2. application.yml配置

```yaml
image-search:
  embedding:
    aliyun:
      # 是否启用阿里云嵌入服务
      enabled: true
      
      # API密钥（建议通过环境变量设置）
      api-key: "${DASHSCOPE_API_KEY:sk-5713eb161252416daa0e89d031b02436}"
      
      # API端点URL
      base-url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      
      # 嵌入模型名称
      model-name: "text-embedding-v4"
      
      # 默认向量维度
      default-dimension: 1024
      
      # 请求超时时间（毫秒）
      timeout-ms: 30000
      
      # 批处理大小
      batch-size: 10
      
      # 最大重试次数
      max-retries: 3
```

### 3. 支持的模型和维度

| 模型名称 | 支持的向量维度 | 最大Token数 | 批处理大小 |
|---------|---------------|------------|-----------|
| text-embedding-v4 | 2048, 1536, 1024, 768, 512, 256, 128, 64 | 8192 | 10 |
| text-embedding-v3 | 1024, 768, 512, 256, 128, 64 | 8192 | 10 |
| text-embedding-v2 | 1536 | 2048 | 25 |
| text-embedding-v1 | 1536 | 2048 | 25 |

## 使用方法

### 1. 基本使用

```java
@Autowired
private EmbeddingService embeddingService;

// 单个文本向量化
String text = "这是一个测试文本";
float[] vector = embeddingService.convertTextToVector(text);

// 指定维度的向量化
float[] vector1024 = embeddingService.convertTextToVector(text, 1024);

// 批量文本向量化
List<String> texts = Arrays.asList("文本1", "文本2", "文本3");
List<float[]> vectors = embeddingService.convertTextsToVectors(texts);
```

### 2. 在向量搜索中使用

VectorSearchServiceImpl已经集成了嵌入服务：

```java
@Service
public class VectorSearchServiceImpl implements VectorSearchService {
    
    @Autowired(required = false)
    private EmbeddingService embeddingService;
    
    private float[] convertTextToVector(String text) {
        // 优先使用真实的嵌入服务
        if (embeddingService != null && embeddingService.isServiceAvailable()) {
            return embeddingService.convertTextToVector(text);
        }
        
        // 回退到模拟实现
        return convertTextToVectorMock(text);
    }
}
```

### 3. 服务可用性检查

```java
// 检查服务是否可用
boolean available = embeddingService.isServiceAvailable();

// 获取支持的维度
List<Integer> dimensions = embeddingService.getSupportedDimensions();

// 获取默认维度
int defaultDim = embeddingService.getDefaultDimension();
```

## 错误处理

### 1. 常见错误类型

- **API密钥无效**: 检查环境变量DASHSCOPE_API_KEY是否正确设置
- **网络超时**: 调整timeout-ms配置或检查网络连接
- **模型不存在**: 确认model-name配置是否正确
- **维度不支持**: 检查dimension参数是否在支持范围内
- **Token超限**: 减少输入文本长度或分批处理

### 2. 重试机制

系统内置了重试机制，可以通过配置调整：

```yaml
image-search:
  embedding:
    aliyun:
      max-retries: 3          # 最大重试次数
      retry-interval-ms: 1000 # 重试间隔（毫秒）
```

### 3. 日志配置

启用详细日志以便调试：

```yaml
image-search:
  embedding:
    aliyun:
      enable-request-logging: true   # 启用请求日志
      enable-response-logging: true  # 启用响应日志

logging:
  level:
    cn.iflytek.imagesearch.infrastructure.embedding: DEBUG
```

## 测试

### 1. 单元测试

运行嵌入服务测试：

```bash
mvn test -Dtest=AliyunEmbeddingServiceImplTest
```

### 2. 集成测试

运行完整的向量搜索测试：

```bash
mvn test -Dtest=VectorSearchServiceImplTest
```

### 3. 手动测试

使用curl测试API连接：

```bash
curl --location 'https://dashscope.aliyuncs.com/compatible-mode/v1/embeddings' \
--header "Authorization: Bearer $DASHSCOPE_API_KEY" \
--header 'Content-Type: application/json' \
--data '{
    "model": "text-embedding-v4",
    "input": "测试文本",
    "dimensions": 1024,
    "encoding_format": "float"
}'
```

## 性能优化

### 1. 批量处理

优先使用批量接口处理多个文本：

```java
// 推荐：批量处理
List<float[]> vectors = embeddingService.convertTextsToVectors(texts);

// 不推荐：逐个处理
List<float[]> vectors = new ArrayList<>();
for (String text : texts) {
    vectors.add(embeddingService.convertTextToVector(text));
}
```

### 2. 缓存策略

考虑对常用查询文本的向量结果进行缓存：

```java
@Cacheable(value = "textVectors", key = "#text")
public float[] convertTextToVector(String text) {
    return embeddingService.convertTextToVector(text);
}
```

## 故障排除

### 1. 服务不可用

检查步骤：
1. 验证API密钥是否正确
2. 检查网络连接
3. 确认配置文件是否正确
4. 查看详细错误日志

### 2. 向量维度不匹配

确保配置的向量维度与模型支持的维度一致：

```yaml
image-search:
  vector:
    vector-dimension: 1024  # 与嵌入服务的默认维度保持一致
  embedding:
    aliyun:
      default-dimension: 1024
```

## 扩展性

### 1. 支持多个嵌入服务

可以通过策略模式支持多个嵌入服务：

```java
@Component
public class EmbeddingServiceSelector {
    
    public EmbeddingService selectService(String provider) {
        switch (provider) {
            case "aliyun": return aliyunEmbeddingService;
            case "openai": return openaiEmbeddingService;
            default: return defaultEmbeddingService;
        }
    }
}
```

### 2. 自定义嵌入服务

实现EmbeddingService接口即可集成其他嵌入服务：

```java
@Service
public class CustomEmbeddingServiceImpl implements EmbeddingService {
    // 实现接口方法
}
```

## 总结

通过本集成方案，系统成功实现了：

1. **真实的文本向量化能力**：集成阿里云百炼嵌入模型
2. **架构解耦**：通过防腐层隔离外部API变化
3. **高可用性**：支持重试机制和回退策略
4. **易于扩展**：标准化的接口设计支持多种嵌入服务
5. **生产就绪**：完善的错误处理、监控和测试

该方案为图片搜索系统提供了强大的语义搜索能力，显著提升了搜索的准确性和用户体验。
