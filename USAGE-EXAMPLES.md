# 阿里云百炼嵌入服务使用示例

本文档提供了阿里云百炼嵌入服务的详细使用示例。

## 1. 基本配置

### 环境变量设置

```bash
# Windows
set DASHSCOPE_API_KEY=sk-5713eb161252416daa0e89d031b02436

# Linux/Mac
export DASHSCOPE_API_KEY=sk-5713eb161252416daa0e89d031b02436
```

### application.yml配置

```yaml
image-search:
  embedding:
    aliyun:
      enabled: true
      api-key: "${DASHSCOPE_API_KEY:sk-5713eb161252416daa0e89d031b02436}"
      base-url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
      model-name: "text-embedding-v4"
      default-dimension: 1024
      timeout-ms: 30000
      batch-size: 10
      max-retries: 3
```

## 2. 代码使用示例

### 基本使用

```java
@Service
public class MyService {
    
    @Autowired
    private EmbeddingService embeddingService;
    
    public void basicUsage() {
        // 检查服务是否可用
        if (embeddingService.isServiceAvailable()) {
            // 单文本向量化
            String text = "这是一个测试文本";
            float[] vector = embeddingService.convertTextToVector(text);
            
            System.out.println("向量维度: " + vector.length);
            System.out.println("前5个元素: " + Arrays.toString(Arrays.copyOf(vector, 5)));
        }
    }
}
```

### 指定维度

```java
public void customDimension() {
    // 获取支持的维度
    List<Integer> supportedDims = embeddingService.getSupportedDimensions();
    System.out.println("支持的维度: " + supportedDims);
    
    // 使用1024维
    float[] vector1024 = embeddingService.convertTextToVector("测试文本", 1024);
    
    // 使用512维（更快，但精度稍低）
    float[] vector512 = embeddingService.convertTextToVector("测试文本", 512);
    
    // 使用2048维（更精确，但更慢）
    float[] vector2048 = embeddingService.convertTextToVector("测试文本", 2048);
}
```

### 批量处理

```java
public void batchProcessing() {
    List<String> texts = Arrays.asList(
        "第一个文档内容",
        "第二个文档内容", 
        "第三个文档内容",
        "第四个文档内容"
    );
    
    // 批量转换（推荐方式）
    List<float[]> vectors = embeddingService.convertTextsToVectors(texts);
    
    System.out.println("处理了 " + vectors.size() + " 个文本");
    for (int i = 0; i < vectors.size(); i++) {
        System.out.println("文本 " + (i+1) + " 的向量维度: " + vectors.get(i).length);
    }
}
```

### 语义搜索应用

```java
@Service
public class SemanticSearchService {
    
    @Autowired
    private EmbeddingService embeddingService;
    
    public List<Document> semanticSearch(String query, List<Document> documents) {
        // 1. 将查询转换为向量
        float[] queryVector = embeddingService.convertTextToVector(query);
        
        // 2. 计算与所有文档的相似度
        List<DocumentSimilarity> similarities = new ArrayList<>();
        
        for (Document doc : documents) {
            float[] docVector = embeddingService.convertTextToVector(doc.getContent());
            double similarity = cosineSimilarity(queryVector, docVector);
            similarities.add(new DocumentSimilarity(doc, similarity));
        }
        
        // 3. 按相似度排序并返回
        return similarities.stream()
                .sorted((a, b) -> Double.compare(b.getSimilarity(), a.getSimilarity()))
                .map(DocumentSimilarity::getDocument)
                .limit(10)
                .collect(Collectors.toList());
    }
    
    private double cosineSimilarity(float[] a, float[] b) {
        double dotProduct = 0.0;
        double normA = 0.0;
        double normB = 0.0;
        
        for (int i = 0; i < a.length; i++) {
            dotProduct += a[i] * b[i];
            normA += a[i] * a[i];
            normB += b[i] * b[i];
        }
        
        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }
}
```

## 3. HTTP API测试

### 检查服务状态

```bash
curl -X GET http://localhost:8099/api/embedding/test/status
```

响应示例：
```json
{
  "serviceAvailable": true,
  "isServiceAvailable": true,
  "defaultDimension": 1024,
  "supportedDimensions": [64, 128, 256, 512, 768, 1024, 1536, 2048],
  "serviceInfo": "AliyunEmbeddingService[model=text-embedding-v4, dimension=1024, endpoint=https://dashscope.aliyuncs.com/compatible-mode/v1, batchSize=10]"
}
```

### 单文本向量化测试

```bash
curl -X POST http://localhost:8099/api/embedding/test/single \
  -H "Content-Type: application/json" \
  -d '{
    "text": "这是一个测试文本",
    "dimension": 1024
  }'
```

响应示例：
```json
{
  "success": true,
  "text": "这是一个测试文本",
  "dimension": 1024,
  "vectorPreview": [0.1234, -0.5678, 0.9012, -0.3456, 0.7890],
  "processingTimeMs": 1250
}
```

### 批量文本向量化测试

```bash
curl -X POST http://localhost:8099/api/embedding/test/batch \
  -H "Content-Type: application/json" \
  -d '{
    "texts": ["文本1", "文本2", "文本3"],
    "dimension": 1024
  }'
```

响应示例：
```json
{
  "success": true,
  "inputCount": 3,
  "outputCount": 3,
  "dimension": 1024,
  "processingTimeMs": 2100,
  "avgTimePerText": 700.0,
  "firstVectorPreview": [0.1234, -0.5678, 0.9012, -0.3456, 0.7890]
}
```

### 连接测试

```bash
curl -X GET http://localhost:8099/api/embedding/test/connection
```

响应示例：
```json
{
  "connected": true,
  "responseTimeMs": 850,
  "message": "连接成功"
}
```

## 4. 错误处理示例

### 服务不可用时的处理

```java
@Service
public class RobustEmbeddingService {
    
    @Autowired(required = false)
    private EmbeddingService embeddingService;
    
    public float[] getTextVector(String text) {
        if (embeddingService != null && embeddingService.isServiceAvailable()) {
            try {
                return embeddingService.convertTextToVector(text);
            } catch (Exception e) {
                log.warn("嵌入服务调用失败，使用回退方案: {}", e.getMessage());
                return getFallbackVector(text);
            }
        } else {
            log.info("嵌入服务不可用，使用回退方案");
            return getFallbackVector(text);
        }
    }
    
    private float[] getFallbackVector(String text) {
        // 简单的回退实现
        Random random = new Random(text.hashCode());
        float[] vector = new float[1024];
        for (int i = 0; i < vector.length; i++) {
            vector[i] = (random.nextFloat() - 0.5f) * 2.0f;
        }
        return vector;
    }
}
```

### 配置验证

```java
@Component
public class EmbeddingConfigValidator {
    
    @Autowired
    private AliyunEmbeddingConfig config;
    
    @PostConstruct
    public void validateConfig() {
        if (!config.isValid()) {
            log.error("嵌入服务配置无效:");
            
            if (config.getApiKey() == null || config.getApiKey().trim().isEmpty()) {
                log.error("- API密钥未设置，请设置环境变量 DASHSCOPE_API_KEY");
            }
            
            if (!config.getSupportedDimensions().contains(config.getDefaultDimension())) {
                log.error("- 默认维度 {} 不被模型 {} 支持", 
                         config.getDefaultDimension(), config.getModelName());
            }
            
            if (config.getBatchSize() > config.getMaxBatchSize()) {
                log.error("- 批处理大小 {} 超过模型限制 {}", 
                         config.getBatchSize(), config.getMaxBatchSize());
            }
        }
    }
}
```

## 5. 性能优化示例

### 缓存向量结果

```java
@Service
public class CachedEmbeddingService {
    
    @Autowired
    private EmbeddingService embeddingService;
    
    private final Map<String, float[]> vectorCache = new ConcurrentHashMap<>();
    
    @Cacheable(value = "textVectors", key = "#text")
    public float[] getCachedVector(String text) {
        return embeddingService.convertTextToVector(text);
    }
    
    public List<float[]> getBatchVectorsWithCache(List<String> texts) {
        List<String> uncachedTexts = new ArrayList<>();
        List<float[]> results = new ArrayList<>();
        
        // 检查缓存
        for (String text : texts) {
            float[] cached = vectorCache.get(text);
            if (cached != null) {
                results.add(cached);
            } else {
                uncachedTexts.add(text);
                results.add(null); // 占位符
            }
        }
        
        // 批量处理未缓存的文本
        if (!uncachedTexts.isEmpty()) {
            List<float[]> newVectors = embeddingService.convertTextsToVectors(uncachedTexts);
            
            // 更新缓存和结果
            int uncachedIndex = 0;
            for (int i = 0; i < results.size(); i++) {
                if (results.get(i) == null) {
                    float[] vector = newVectors.get(uncachedIndex++);
                    results.set(i, vector);
                    vectorCache.put(texts.get(i), vector);
                }
            }
        }
        
        return results;
    }
}
```

### 异步处理

```java
@Service
public class AsyncEmbeddingService {
    
    @Autowired
    private EmbeddingService embeddingService;
    
    @Async
    public CompletableFuture<float[]> convertTextToVectorAsync(String text) {
        float[] vector = embeddingService.convertTextToVector(text);
        return CompletableFuture.completedFuture(vector);
    }
    
    public List<float[]> convertTextsToVectorsParallel(List<String> texts) {
        List<CompletableFuture<float[]>> futures = texts.stream()
                .map(this::convertTextToVectorAsync)
                .collect(Collectors.toList());
        
        return futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
    }
}
```

## 6. 监控和日志

### 性能监控

```java
@Component
public class EmbeddingMetrics {
    
    private final AtomicLong requestCount = new AtomicLong(0);
    private final AtomicLong totalTime = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    
    public float[] convertWithMetrics(String text) {
        long startTime = System.currentTimeMillis();
        requestCount.incrementAndGet();
        
        try {
            float[] result = embeddingService.convertTextToVector(text);
            long duration = System.currentTimeMillis() - startTime;
            totalTime.addAndGet(duration);
            
            log.debug("嵌入转换完成: 文本长度={}, 耗时={}ms", text.length(), duration);
            return result;
            
        } catch (Exception e) {
            errorCount.incrementAndGet();
            log.error("嵌入转换失败: {}", e.getMessage());
            throw e;
        }
    }
    
    public void printStats() {
        long requests = requestCount.get();
        long errors = errorCount.get();
        long avgTime = requests > 0 ? totalTime.get() / requests : 0;
        
        log.info("嵌入服务统计: 请求数={}, 错误数={}, 平均耗时={}ms, 成功率={}%", 
                requests, errors, avgTime, 
                requests > 0 ? (requests - errors) * 100.0 / requests : 0);
    }
}
```

这些示例展示了如何在实际项目中使用阿里云百炼嵌入服务，包括基本用法、错误处理、性能优化和监控等方面。
