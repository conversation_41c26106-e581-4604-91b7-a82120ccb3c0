package cn.iflytek.imagesearch.infrastructure.embedding;

import cn.iflytek.imagesearch.domain.service.embedding.EmbeddingService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 阿里云嵌入服务测试类
 * 
 * 注意：这些测试需要有效的阿里云API密钥才能运行。
 * 如果没有配置API密钥，测试将被跳过或使用模拟实现。
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
@SpringBootTest
@ActiveProfiles("test")
public class AliyunEmbeddingServiceImplTest {

    @Autowired(required = false)
    private EmbeddingService embeddingService;

    @Test
    public void testServiceAvailability() {
        if (embeddingService != null) {
            // 测试服务是否可用（这个测试可能会因为网络或API密钥问题而失败）
            boolean available = embeddingService.isServiceAvailable();
            System.out.println("嵌入服务可用性: " + available);
            
            // 获取默认维度
            int defaultDimension = embeddingService.getDefaultDimension();
            System.out.println("默认向量维度: " + defaultDimension);
            assertTrue(defaultDimension > 0);
            
            // 获取支持的维度
            List<Integer> supportedDimensions = embeddingService.getSupportedDimensions();
            System.out.println("支持的向量维度: " + supportedDimensions);
            assertFalse(supportedDimensions.isEmpty());
        } else {
            System.out.println("嵌入服务未配置或不可用，跳过测试");
        }
    }

    @Test
    public void testSingleTextConversion() {
        if (embeddingService != null && embeddingService.isServiceAvailable()) {
            String testText = "这是一个测试文本";
            
            // 测试默认维度转换
            float[] vector = embeddingService.convertTextToVector(testText);
            if (vector != null) {
                System.out.println("文本向量转换成功，维度: " + vector.length);
                assertTrue(vector.length > 0);
                
                // 检查向量值是否合理
                boolean hasNonZero = false;
                for (float value : vector) {
                    if (value != 0.0f) {
                        hasNonZero = true;
                        break;
                    }
                }
                assertTrue(hasNonZero, "向量应该包含非零值");
            } else {
                System.out.println("文本向量转换返回null，可能是API调用失败");
            }
            
            // 测试指定维度转换
            int targetDimension = 512;
            float[] vectorWithDimension = embeddingService.convertTextToVector(testText, targetDimension);
            if (vectorWithDimension != null) {
                assertEquals(targetDimension, vectorWithDimension.length);
                System.out.println("指定维度向量转换成功: " + targetDimension);
            }
        } else {
            System.out.println("嵌入服务不可用，跳过单文本转换测试");
        }
    }

    @Test
    public void testBatchTextConversion() {
        if (embeddingService != null && embeddingService.isServiceAvailable()) {
            List<String> testTexts = Arrays.asList(
                    "第一个测试文本",
                    "第二个测试文本",
                    "第三个测试文本"
            );
            
            // 测试批量转换
            List<float[]> vectors = embeddingService.convertTextsToVectors(testTexts);
            if (vectors != null && !vectors.isEmpty()) {
                System.out.println("批量文本向量转换成功，数量: " + vectors.size());
                assertEquals(testTexts.size(), vectors.size());
                
                // 检查每个向量
                for (int i = 0; i < vectors.size(); i++) {
                    float[] vector = vectors.get(i);
                    assertNotNull(vector, "第" + (i + 1) + "个向量不应为null");
                    assertTrue(vector.length > 0, "第" + (i + 1) + "个向量维度应大于0");
                }
            } else {
                System.out.println("批量文本向量转换返回空结果，可能是API调用失败");
            }
        } else {
            System.out.println("嵌入服务不可用，跳过批量转换测试");
        }
    }

    @Test
    public void testEmptyTextHandling() {
        if (embeddingService != null) {
            // 测试空文本处理
            float[] emptyVector = embeddingService.convertTextToVector("");
            assertNull(emptyVector, "空文本应该返回null");
            
            float[] nullVector = embeddingService.convertTextToVector(null);
            assertNull(nullVector, "null文本应该返回null");
            
            // 测试空列表处理
            List<float[]> emptyVectors = embeddingService.convertTextsToVectors(Arrays.asList());
            assertNotNull(emptyVectors);
            assertTrue(emptyVectors.isEmpty(), "空列表应该返回空结果");
            
            System.out.println("空文本处理测试通过");
        }
    }
}
