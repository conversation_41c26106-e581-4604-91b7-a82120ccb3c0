spring:
  # 测试数据库配置 - 使用H2内存数据库
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
    username: sa
    password:
    driver-class-name: org.h2.Driver
    hikari:
      maximum-pool-size: 5
      minimum-idle: 1
      connection-timeout: 30000

  # H2数据库控制台（可选，用于调试）
  h2:
    console:
      enabled: true
      path: /h2-console

  # MyBatis配置
  mybatis:
    mapper-locations: classpath:mapper/*.xml
    type-aliases-package: cn.iflytek.imagesearch.infrastructure.persistence.po
    configuration:
      map-underscore-to-camel-case: true
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 测试环境嵌入服务配置
image-search:
  embedding:
    aliyun:
      # 测试环境默认禁用真实API调用
      enabled: false

      # 测试用的API密钥（如果需要测试真实API）
      api-key: "${DASHSCOPE_API_KEY:test-key}"

      # API端点URL
      base-url: "https://dashscope.aliyuncs.com/compatible-mode/v1"

      # 嵌入模型名称
      model-name: "text-embedding-v4"

      # 默认向量维度
      default-dimension: 1024

      # 请求超时时间（毫秒）
      timeout-ms: 10000

      # 批处理大小
      batch-size: 5

      # 最大重试次数
      max-retries: 1

      # 重试间隔时间（毫秒）
      retry-interval-ms: 500

      # 启用日志用于调试
      enable-request-logging: true
      enable-response-logging: true

# 日志配置
logging:
  level:
    cn.iflytek.imagesearch: DEBUG
    cn.iflytek.imagesearch.infrastructure.embedding: TRACE
    org.springframework.jdbc: DEBUG
