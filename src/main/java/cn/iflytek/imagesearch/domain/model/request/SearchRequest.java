package cn.iflytek.imagesearch.domain.model.request;

import cn.iflytek.imagesearch.domain.model.entry.ImageMetadataFilter;
import cn.iflytek.imagesearch.domain.model.entry.SearchStrategy;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Data;

/**
 * 语义搜索图片请求
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SearchRequest {

    @JsonProperty("query")
    @JsonPropertyDescription("语义搜索查询文本，用于描述要搜索的图片内容，例如：'一只可爱的小猫'、'蓝色的天空'等")
    private String query;

    @JsonProperty("metadata")
    @JsonPropertyDescription("元数据筛选条件，用于按图片的结构化信息进行筛选，如尺寸、格式、时间等")
    private ImageMetadataFilter metadata;

    @JsonProperty("limit")
    @JsonPropertyDescription("返回结果数量限制，默认2，最大不超过100")
    private int limit = 2;

    @JsonProperty("strategy")
    @JsonPropertyDescription("搜索策略：SEMANTIC_ONLY(纯语义搜索)、METADATA_ONLY(纯元数据筛选)、HYBRID(混合搜索)、SEMANTIC_FIRST(语义优先)、METADATA_FIRST(元数据优先)")
    private SearchStrategy strategy = SearchStrategy.HYBRID;
}
