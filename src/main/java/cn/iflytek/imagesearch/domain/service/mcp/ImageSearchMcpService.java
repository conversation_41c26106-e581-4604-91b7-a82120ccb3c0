package cn.iflytek.imagesearch.domain.service.mcp;


import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import cn.iflytek.imagesearch.domain.model.request.ImageDetailRequest;
import cn.iflytek.imagesearch.domain.model.request.SearchRequest;
import cn.iflytek.imagesearch.domain.model.response.ImageDetailResponse;
import cn.iflytek.imagesearch.domain.model.response.SearchResult;
import cn.iflytek.imagesearch.domain.service.search.ImageSearchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

/**
 * 图片搜索MCP服务
 * 提供图片搜索相关的MCP工具
 */
@Component
public class ImageSearchMcpService {

    private final Logger log = LoggerFactory.getLogger(ImageSearchMcpService.class);

    private final ImageSearchService imageSearchService;

    public ImageSearchMcpService(ImageSearchService imageSearchService) {
        this.imageSearchService = imageSearchService;
    }

    /**
     * 语义搜索图片工具
     * 基于自然语言描述进行智能图片搜索
     */
    @Tool(description = "基于自然语言描述进行智能图片搜索。支持复杂的语义理解，能够根据描述内容找到相关图片。")
    public SearchResult<ImageEntity> semanticSearchImages(SearchRequest request) {
        log.info("执行语义搜索图片，请求参数: {}", request);

        try {
            SearchResult<ImageEntity> response = imageSearchService.semanticSearch(request);
            return response;
        } catch (Exception e) {
            log.error("语义搜索图片失败", e);
            SearchResult<ImageEntity> errorResponse = new SearchResult();
            return errorResponse;
        }
    }





}
