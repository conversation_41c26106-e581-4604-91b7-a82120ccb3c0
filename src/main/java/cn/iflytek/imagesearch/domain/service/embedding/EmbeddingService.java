package cn.iflytek.imagesearch.domain.service.embedding;

import java.util.List;

/**
 * 嵌入服务接口
 * 
 * 定义文本向量化的标准操作，提供将文本转换为数值向量的能力。
 * 该接口遵循领域驱动设计原则，定义了嵌入服务的核心业务能力，
 * 具体实现可以基于不同的嵌入模型（如阿里云百炼、OpenAI等）。
 * 
 * 主要功能：
 * 1. 单文本向量化：将单个文本转换为向量表示
 * 2. 批量文本向量化：高效处理多个文本的向量转换
 * 3. 自定义维度：支持指定向量维度
 * 4. 服务可用性检查：检测嵌入服务是否正常工作
 * 5. 配置查询：获取服务支持的维度和默认配置
 * 
 * 技术特点：
 * - 支持多种向量维度（64, 128, 256, 512, 768, 1024, 1536, 2048等）
 * - 批量处理优化，提高大规模文本处理效率
 * - 错误处理和重试机制
 * - 服务降级和回退策略
 * 
 * 使用场景：
 * - 语义搜索：将查询文本转换为向量进行相似度计算
 * - 文本分类：基于向量表示进行文本分类
 * - 推荐系统：计算文本内容的相似度
 * - 聚类分析：基于向量进行文本聚类
 * 
 * 注意事项：
 * - 向量维度必须与下游应用保持一致
 * - 批量处理时注意API调用限制
 * - 相同文本在相同配置下应产生相同向量
 * - 服务不可用时应有合适的降级策略
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
public interface EmbeddingService {

    /**
     * 将单个文本转换为向量（使用默认维度）
     * 
     * 使用服务的默认向量维度将输入文本转换为数值向量表示。
     * 该方法适用于大多数标准应用场景，无需指定特定的向量维度。
     * 
     * 处理流程：
     * 1. 验证输入文本的有效性
     * 2. 调用嵌入模型API进行向量转换
     * 3. 返回标准化的向量表示
     * 4. 异常情况下返回null或抛出异常
     * 
     * @param text 待转换的文本内容
     *             - 不能为null或空字符串
     *             - 长度应在模型支持范围内（通常8192 tokens以内）
     *             - 支持中英文及多种语言
     * @return 文本的向量表示，使用默认维度
     *         - 向量元素为float类型，通常范围在[-1, 1]之间
     *         - 向量长度等于默认维度设置
     *         - 输入无效或转换失败时返回null
     * @throws IllegalArgumentException 当输入文本为null或空时抛出
     * @throws RuntimeException 当API调用失败或网络异常时抛出
     * 
     * @example
     * <pre>
     * EmbeddingService service = ...;
     * float[] vector = service.convertTextToVector("这是一个测试文本");
     * if (vector != null) {
     *     System.out.println("向量维度: " + vector.length);
     *     System.out.println("第一个元素: " + vector[0]);
     * }
     * </pre>
     */
    float[] convertTextToVector(String text);

    /**
     * 将单个文本转换为指定维度的向量
     * 
     * 使用指定的向量维度将输入文本转换为数值向量表示。
     * 该方法允许用户根据具体应用需求选择合适的向量维度，
     * 不同维度在表达能力和计算效率之间提供不同的平衡。
     * 
     * 维度选择建议：
     * - 64-256维：适用于简单分类和快速检索
     * - 512-768维：适用于一般语义搜索应用
     * - 1024-1536维：适用于高精度语义理解
     * - 2048维：适用于复杂语义分析和研究
     * 
     * @param text 待转换的文本内容，要求与convertTextToVector(String)相同
     * @param dimension 目标向量维度
     *                  - 必须是服务支持的维度值
     *                  - 常见支持维度：64, 128, 256, 512, 768, 1024, 1536, 2048
     *                  - 具体支持维度可通过getSupportedDimensions()查询
     * @return 指定维度的文本向量表示
     *         - 向量长度等于指定的dimension参数
     *         - 其他特性与convertTextToVector(String)相同
     * @throws IllegalArgumentException 当文本无效或维度不支持时抛出
     * @throws RuntimeException 当API调用失败时抛出
     * 
     * @example
     * <pre>
     * // 获取1024维向量用于高精度搜索
     * float[] vector1024 = service.convertTextToVector("查询文本", 1024);
     * 
     * // 获取256维向量用于快速检索
     * float[] vector256 = service.convertTextToVector("查询文本", 256);
     * </pre>
     */
    float[] convertTextToVector(String text, int dimension);

    /**
     * 批量将多个文本转换为向量（使用默认维度）
     * 
     * 高效地将多个文本同时转换为向量表示，相比逐个转换具有更好的性能。
     * 该方法利用批量API调用减少网络开销，提高大规模文本处理的效率。
     * 
     * 性能优势：
     * - 减少HTTP请求次数，降低网络延迟
     * - 批量处理优化，提高API吞吐量
     * - 更好的资源利用率
     * 
     * 批量限制：
     * - 单次批量大小通常限制在10-25个文本之间
     * - 超过限制时会自动分批处理
     * - 总文本长度不能超过API限制
     * 
     * @param texts 待转换的文本列表
     *              - 不能为null或空列表
     *              - 列表中的每个文本都不能为null或空字符串
     *              - 建议批量大小在10-100之间以获得最佳性能
     *              - 过大的批量可能导致超时或内存问题
     * @return 对应的向量列表，与输入文本列表顺序一致
     *         - 返回列表长度等于输入文本列表长度
     *         - 每个向量的维度等于默认维度设置
     *         - 如果某个文本转换失败，对应位置可能为null
     *         - 输入为空列表时返回空列表
     * @throws IllegalArgumentException 当输入列表为null或包含无效文本时抛出
     * @throws RuntimeException 当批量API调用失败时抛出
     * 
     * @example
     * <pre>
     * List<String> texts = Arrays.asList(
     *     "第一个文本",
     *     "第二个文本", 
     *     "第三个文本"
     * );
     * 
     * List<float[]> vectors = service.convertTextsToVectors(texts);
     * for (int i = 0; i < vectors.size(); i++) {
     *     System.out.println("文本 " + (i+1) + " 的向量维度: " + vectors.get(i).length);
     * }
     * </pre>
     */
    List<float[]> convertTextsToVectors(List<String> texts);

    /**
     * 批量将多个文本转换为指定维度的向量
     * 
     * 结合批量处理和自定义维度的优势，为大规模文本处理提供最大的灵活性。
     * 该方法适用于需要特定向量维度的批量文本处理场景。
     * 
     * @param texts 待转换的文本列表，要求与convertTextsToVectors(List)相同
     * @param dimension 目标向量维度，要求与convertTextToVector(String, int)相同
     * @return 指定维度的向量列表，与输入文本列表顺序一致
     * @throws IllegalArgumentException 当输入无效或维度不支持时抛出
     * @throws RuntimeException 当批量API调用失败时抛出
     * 
     * @example
     * <pre>
     * List<String> documents = loadDocuments();
     * List<float[]> vectors = service.convertTextsToVectors(documents, 1024);
     * // 将向量存储到向量数据库中
     * vectorDatabase.store(vectors);
     * </pre>
     */
    List<float[]> convertTextsToVectors(List<String> texts, int dimension);

    /**
     * 检查嵌入服务是否可用
     * 
     * 通过发送测试请求验证嵌入服务的可用性，用于健康检查和服务监控。
     * 该方法可以用于实现服务降级、负载均衡和故障转移等功能。
     * 
     * 检查内容：
     * - API连接是否正常
     * - 认证信息是否有效
     * - 服务是否响应正常
     * - 基本功能是否可用
     * 
     * @return true表示服务可用，false表示服务不可用
     *         - 网络连接正常且API响应正确时返回true
     *         - API密钥无效、网络异常或服务故障时返回false
     *         - 该方法不应抛出异常，而是通过返回值表示状态
     * 
     * @example
     * <pre>
     * if (embeddingService.isServiceAvailable()) {
     *     // 使用真实的嵌入服务
     *     float[] vector = embeddingService.convertTextToVector(text);
     * } else {
     *     // 使用备用方案或缓存
     *     float[] vector = fallbackEmbeddingService.convertTextToVector(text);
     * }
     * </pre>
     */
    boolean isServiceAvailable();

    /**
     * 获取服务支持的向量维度列表
     * 
     * 返回当前嵌入服务支持的所有向量维度选项，用于动态配置和参数验证。
     * 不同的嵌入模型支持不同的维度范围，该方法提供了查询接口。
     * 
     * @return 支持的维度列表，按从小到大排序
     *         - 包含所有可用的维度选项
     *         - 通常包括：[64, 128, 256, 512, 768, 1024, 1536, 2048]
     *         - 具体支持维度取决于底层模型
     *         - 服务不可用时可能返回空列表
     * 
     * @example
     * <pre>
     * List<Integer> dimensions = service.getSupportedDimensions();
     * System.out.println("支持的维度: " + dimensions);
     * // 输出: 支持的维度: [64, 128, 256, 512, 768, 1024, 1536, 2048]
     * </pre>
     */
    List<Integer> getSupportedDimensions();

    /**
     * 获取默认向量维度
     * 
     * 返回服务的默认向量维度设置，用于不指定维度的转换操作。
     * 默认维度通常在性能和精度之间提供良好的平衡。
     * 
     * @return 默认向量维度
     *         - 通常为1024维，提供良好的语义表达能力
     *         - 具体值可通过配置文件调整
     *         - 服务不可用时可能返回0或负数
     * 
     * @example
     * <pre>
     * int defaultDim = service.getDefaultDimension();
     * System.out.println("默认维度: " + defaultDim);
     * // 输出: 默认维度: 1024
     * </pre>
     */
    int getDefaultDimension();
}
