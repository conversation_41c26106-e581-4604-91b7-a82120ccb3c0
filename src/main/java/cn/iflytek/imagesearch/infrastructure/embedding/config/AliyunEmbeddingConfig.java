package cn.iflytek.imagesearch.infrastructure.embedding.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * 阿里云百炼嵌入服务配置类
 * 
 * 管理阿里云百炼嵌入模型的所有配置参数，包括API密钥、端点URL、
 * 模型参数、超时设置、重试策略等。该配置类支持通过application.yml
 * 文件进行配置，并提供合理的默认值。
 * 
 * 配置项说明：
 * - API认证：API密钥和端点配置
 * - 模型参数：模型名称、向量维度等
 * - 性能参数：超时时间、批处理大小等
 * - 重试策略：重试次数、重试间隔等
 * - 日志控制：请求和响应日志开关
 * 
 * 配置示例：
 * ```yaml
 * image-search:
 *   embedding:
 *     aliyun:
 *       enabled: true
 *       api-key: "${DASHSCOPE_API_KEY:}"
 *       base-url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
 *       model-name: "text-embedding-v4"
 *       default-dimension: 1024
 *       timeout-ms: 30000
 *       batch-size: 10
 *       max-retries: 3
 * ```
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "image-search.embedding.aliyun")
public class AliyunEmbeddingConfig {

    /**
     * 是否启用阿里云嵌入服务
     * 
     * 控制是否启用阿里云百炼嵌入服务。当设置为false时，
     * 相关的Bean不会被创建，系统会回退到其他嵌入服务或模拟实现。
     * 
     * 默认值：false（需要显式启用）
     */
    private boolean enabled = false;

    /**
     * 阿里云API密钥
     * 
     * 用于访问阿里云百炼服务的API密钥。建议通过环境变量
     * DASHSCOPE_API_KEY设置，避免在配置文件中明文存储。
     * 
     * 获取方式：
     * 1. 登录阿里云百炼控制台
     * 2. 在API密钥管理页面创建或查看密钥
     * 3. 设置环境变量或配置文件
     * 
     * 安全注意：
     * - 不要在代码中硬编码API密钥
     * - 使用环境变量或安全的配置管理系统
     * - 定期轮换API密钥
     */
    private String apiKey;

    /**
     * API端点URL
     * 
     * 阿里云百炼嵌入服务的API端点地址。支持OpenAI兼容模式，
     * 可以使用标准的OpenAI SDK进行调用。
     * 
     * 可选端点：
     * - OpenAI兼容模式：https://dashscope.aliyuncs.com/compatible-mode/v1
     * - DashScope原生模式：https://dashscope.aliyuncs.com/api/v1
     * 
     * 默认使用OpenAI兼容模式，便于集成和迁移。
     */
    private String baseUrl = "https://dashscope.aliyuncs.com/compatible-mode/v1";

    /**
     * 嵌入模型名称
     * 
     * 指定使用的阿里云百炼嵌入模型。不同模型在性能、精度、
     * 支持语言和向量维度方面有所差异。
     * 
     * 可选模型：
     * - text-embedding-v4：最新模型，支持100+语言，2048维
     * - text-embedding-v3：支持50+语言，1024维
     * - text-embedding-v2：支持多语言，1536维
     * - text-embedding-v1：基础模型，1536维
     * 
     * 推荐使用text-embedding-v4获得最佳效果。
     */
    private String modelName = "text-embedding-v4";

    /**
     * 默认向量维度
     * 
     * 当不指定维度时使用的默认向量维度。不同维度在表达能力
     * 和计算效率之间提供不同的平衡。
     * 
     * 维度选择建议：
     * - 1024维：推荐默认值，平衡性能和精度
     * - 512维：适用于性能敏感场景
     * - 2048维：适用于高精度要求场景
     * 
     * 注意：必须是模型支持的维度值。
     */
    private int defaultDimension = 1024;

    /**
     * 请求超时时间（毫秒）
     * 
     * API请求的超时时间设置。包括连接超时和读取超时。
     * 过短可能导致请求失败，过长可能影响用户体验。
     * 
     * 建议值：
     * - 开发环境：30000ms（30秒）
     * - 生产环境：15000ms（15秒）
     * - 批量处理：60000ms（60秒）
     */
    private long timeoutMs = 30000L;

    /**
     * 批处理大小
     * 
     * 单次批量请求中包含的最大文本数量。受API限制，
     * text-embedding-v4模型最大支持10个文本的批量处理。
     * 
     * 不同模型的限制：
     * - text-embedding-v4/v3：最大10个
     * - text-embedding-v2/v1：最大25个
     * 
     * 建议设置为模型支持的最大值以获得最佳性能。
     */
    private int batchSize = 10;

    /**
     * 最大重试次数
     * 
     * API调用失败时的最大重试次数。适当的重试可以提高
     * 服务的可靠性，但过多重试可能导致延迟增加。
     * 
     * 重试场景：
     * - 网络临时故障
     * - 服务端临时不可用
     * - 限流错误（429状态码）
     * 
     * 不重试场景：
     * - 认证失败（401状态码）
     * - 参数错误（400状态码）
     * - 配额耗尽
     */
    private int maxRetries = 3;

    /**
     * 重试间隔时间（毫秒）
     * 
     * 重试之间的等待时间。使用固定间隔策略，
     * 避免对服务端造成过大压力。
     * 
     * 建议值：
     * - 网络错误：1000ms
     * - 限流错误：2000ms
     * - 服务错误：3000ms
     */
    private long retryIntervalMs = 1000L;

    /**
     * 是否启用请求日志
     * 
     * 控制是否记录API请求的详细信息，包括请求URL、
     * 请求头、请求体等。用于调试和监控。
     * 
     * 注意：启用后可能记录敏感信息，生产环境请谨慎使用。
     */
    private boolean enableRequestLogging = false;

    /**
     * 是否启用响应日志
     * 
     * 控制是否记录API响应的详细信息，包括响应状态、
     * 响应头、响应体等。用于调试和监控。
     * 
     * 注意：响应体可能包含大量向量数据，启用后会增加日志量。
     */
    private boolean enableResponseLogging = false;

    /**
     * 获取支持的向量维度列表
     * 
     * 根据配置的模型名称返回支持的向量维度列表。
     * 不同模型支持不同的维度范围。
     * 
     * @return 支持的维度列表
     */
    public List<Integer> getSupportedDimensions() {
        switch (modelName) {
            case "text-embedding-v4":
                return Arrays.asList(64, 128, 256, 512, 768, 1024, 1536, 2048);
            case "text-embedding-v3":
                return Arrays.asList(64, 128, 256, 512, 768, 1024);
            case "text-embedding-v2":
            case "text-embedding-v1":
                return Arrays.asList(1536);
            default:
                return Arrays.asList(1024); // 默认支持1024维
        }
    }

    /**
     * 获取模型的最大批处理大小
     * 
     * 根据配置的模型名称返回最大批处理大小。
     * 
     * @return 最大批处理大小
     */
    public int getMaxBatchSize() {
        switch (modelName) {
            case "text-embedding-v4":
            case "text-embedding-v3":
                return 10;
            case "text-embedding-v2":
            case "text-embedding-v1":
                return 25;
            default:
                return 10; // 默认值
        }
    }

    /**
     * 验证配置的有效性
     * 
     * 检查关键配置项是否正确设置，用于启动时的配置验证。
     * 
     * @return 配置是否有效
     */
    public boolean isValid() {
        return enabled && 
               apiKey != null && !apiKey.trim().isEmpty() &&
               baseUrl != null && !baseUrl.trim().isEmpty() &&
               modelName != null && !modelName.trim().isEmpty() &&
               defaultDimension > 0 &&
               getSupportedDimensions().contains(defaultDimension) &&
               timeoutMs > 0 &&
               batchSize > 0 && batchSize <= getMaxBatchSize() &&
               maxRetries >= 0 &&
               retryIntervalMs >= 0;
    }

    /**
     * 获取完整的嵌入API端点URL
     * 
     * @return 嵌入API的完整URL
     */
    public String getEmbeddingEndpoint() {
        return baseUrl + "/embeddings";
    }
}
