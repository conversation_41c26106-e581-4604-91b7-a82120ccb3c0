package cn.iflytek.imagesearch.infrastructure.embedding.config;

import cn.iflytek.imagesearch.domain.service.embedding.EmbeddingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * 嵌入服务启动检查
 * 
 * 在应用启动时检查嵌入服务的配置和可用性，
 * 提供早期的错误发现和诊断信息。
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
@Slf4j
@Component
@ConditionalOnProperty(
    prefix = "image-search.embedding.aliyun", 
    name = "enabled", 
    havingValue = "true"
)
public class EmbeddingStartupCheck implements ApplicationRunner {

    @Autowired(required = false)
    private EmbeddingService embeddingService;

    @Autowired
    private AliyunEmbeddingConfig config;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始嵌入服务启动检查...");
        
        // 检查配置
        checkConfiguration();
        
        // 检查服务可用性
        checkServiceAvailability();
        
        log.info("嵌入服务启动检查完成");
    }

    /**
     * 检查配置有效性
     */
    private void checkConfiguration() {
        log.info("检查嵌入服务配置...");
        
        if (!config.isValid()) {
            log.error("嵌入服务配置无效: {}", config);
            log.error("请检查以下配置项:");
            log.error("- API密钥是否正确设置");
            log.error("- 端点URL是否有效");
            log.error("- 模型名称是否正确");
            log.error("- 向量维度是否支持");
            return;
        }
        
        log.info("配置检查通过:");
        log.info("- 模型: {}", config.getModelName());
        log.info("- 端点: {}", config.getBaseUrl());
        log.info("- 默认维度: {}", config.getDefaultDimension());
        log.info("- 支持维度: {}", config.getSupportedDimensions());
        log.info("- 批处理大小: {}", config.getBatchSize());
        log.info("- 超时时间: {}ms", config.getTimeoutMs());
        log.info("- 最大重试: {}", config.getMaxRetries());
    }

    /**
     * 检查服务可用性
     */
    private void checkServiceAvailability() {
        log.info("检查嵌入服务可用性...");
        
        if (embeddingService == null) {
            log.warn("嵌入服务Bean未创建，可能是配置问题");
            return;
        }
        
        try {
            boolean available = embeddingService.isServiceAvailable();
            if (available) {
                log.info("嵌入服务可用性检查通过");
                
                // 执行简单的功能测试
                performSimpleTest();
            } else {
                log.warn("嵌入服务不可用，请检查:");
                log.warn("- API密钥是否正确");
                log.warn("- 网络连接是否正常");
                log.warn("- 服务端点是否可访问");
                log.warn("系统将回退到模拟实现");
            }
        } catch (Exception e) {
            log.error("嵌入服务可用性检查失败: {}", e.getMessage());
            log.warn("系统将回退到模拟实现");
        }
    }

    /**
     * 执行简单的功能测试
     */
    private void performSimpleTest() {
        try {
            log.info("执行嵌入服务功能测试...");
            
            String testText = "测试文本";
            float[] vector = embeddingService.convertTextToVector(testText);
            
            if (vector != null && vector.length > 0) {
                log.info("功能测试通过: 文本='{}', 向量维度={}", testText, vector.length);
                log.info("向量预览: [{}, {}, {}, ...]", 
                        vector[0], 
                        vector.length > 1 ? vector[1] : 0, 
                        vector.length > 2 ? vector[2] : 0);
            } else {
                log.warn("功能测试失败: 返回空向量");
            }
        } catch (Exception e) {
            log.warn("功能测试失败: {}", e.getMessage());
        }
    }
}
