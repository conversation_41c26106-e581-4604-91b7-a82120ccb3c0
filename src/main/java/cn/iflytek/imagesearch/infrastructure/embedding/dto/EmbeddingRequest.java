package cn.iflytek.imagesearch.infrastructure.embedding.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 阿里云百炼嵌入API请求DTO
 * 
 * 封装发送给阿里云百炼嵌入服务的请求参数，支持OpenAI兼容格式。
 * 该DTO遵循阿里云百炼API规范，支持单文本和批量文本的向量化请求。
 * 
 * API文档参考：
 * https://help.aliyun.com/zh/model-studio/embedding
 * 
 * 请求格式示例：
 * ```json
 * {
 *   "model": "text-embedding-v4",
 *   "input": "这是一个测试文本",
 *   "dimensions": 1024,
 *   "encoding_format": "float"
 * }
 * ```
 * 
 * 批量请求示例：
 * ```json
 * {
 *   "model": "text-embedding-v4", 
 *   "input": ["文本1", "文本2", "文本3"],
 *   "dimensions": 1024,
 *   "encoding_format": "float"
 * }
 * ```
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmbeddingRequest {

    /**
     * 嵌入模型名称
     * 
     * 指定要使用的阿里云百炼嵌入模型。不同模型在性能、
     * 精度、支持语言和向量维度方面有所差异。
     * 
     * 支持的模型：
     * - text-embedding-v4：最新模型，支持100+语言，最大2048维
     * - text-embedding-v3：支持50+语言，最大1024维  
     * - text-embedding-v2：支持多语言，1536维
     * - text-embedding-v1：基础模型，1536维
     * 
     * 必填字段，不能为null或空字符串。
     */
    @JsonProperty("model")
    private String model;

    /**
     * 输入文本内容
     * 
     * 待转换为向量的文本内容，支持以下格式：
     * 1. 单个字符串：直接传入文本内容
     * 2. 字符串数组：批量处理多个文本
     * 
     * 限制条件：
     * - 单个文本最大长度：8192 tokens
     * - 批量处理最大数量：10个文本（text-embedding-v4）
     * - 不能为null、空字符串或空数组
     * - 支持中英文及多种语言
     * 
     * 使用Object类型以支持String和List<String>两种格式。
     * 实际使用时会根据具体类型进行序列化。
     */
    @JsonProperty("input")
    private Object input;

    /**
     * 向量维度
     * 
     * 指定生成向量的维度大小。不同维度在表达能力和
     * 计算效率之间提供不同的平衡。
     * 
     * 支持的维度（根据模型而定）：
     * - text-embedding-v4：64, 128, 256, 512, 768, 1024, 1536, 2048
     * - text-embedding-v3：64, 128, 256, 512, 768, 1024
     * - text-embedding-v2/v1：1536
     * 
     * 可选字段，不指定时使用模型默认维度。
     * 对于text-embedding-v4，默认为1024维。
     */
    @JsonProperty("dimensions")
    private Integer dimensions;

    /**
     * 编码格式
     * 
     * 指定返回向量的数据格式。目前支持：
     * - "float"：浮点数格式（推荐）
     * - "base64"：Base64编码格式（节省传输带宽）
     * 
     * 默认使用"float"格式，便于直接使用和调试。
     * Base64格式适用于网络带宽受限的场景。
     */
    @JsonProperty("encoding_format")
    private String encodingFormat = "float";

    /**
     * 用户标识
     * 
     * 可选的用户标识符，用于API使用统计和监控。
     * 有助于跟踪不同用户或应用的API使用情况。
     * 
     * 可选字段，通常用于多租户场景。
     */
    @JsonProperty("user")
    private String user;

    /**
     * 创建单文本请求
     * 
     * 便捷方法，用于创建单个文本的向量化请求。
     * 
     * @param model 模型名称
     * @param text 输入文本
     * @param dimensions 向量维度（可选）
     * @return 请求对象
     */
    public static EmbeddingRequest createSingleTextRequest(String model, String text, Integer dimensions) {
        return EmbeddingRequest.builder()
                .model(model)
                .input(text)
                .dimensions(dimensions)
                .encodingFormat("float")
                .build();
    }

    /**
     * 创建批量文本请求
     * 
     * 便捷方法，用于创建多个文本的批量向量化请求。
     * 
     * @param model 模型名称
     * @param texts 输入文本列表
     * @param dimensions 向量维度（可选）
     * @return 请求对象
     */
    public static EmbeddingRequest createBatchTextRequest(String model, List<String> texts, Integer dimensions) {
        return EmbeddingRequest.builder()
                .model(model)
                .input(texts)
                .dimensions(dimensions)
                .encodingFormat("float")
                .build();
    }

    /**
     * 验证请求参数的有效性
     * 
     * 检查必填字段和参数约束，确保请求能够被API正确处理。
     * 
     * @return 验证是否通过
     */
    public boolean isValid() {
        if (model == null || model.trim().isEmpty()) {
            return false;
        }
        
        if (input == null) {
            return false;
        }
        
        // 验证输入格式
        if (input instanceof String) {
            String text = (String) input;
            if (text.trim().isEmpty()) {
                return false;
            }
        } else if (input instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> texts = (List<String>) input;
            if (texts.isEmpty()) {
                return false;
            }
            for (String text : texts) {
                if (text == null || text.trim().isEmpty()) {
                    return false;
                }
            }
        } else {
            return false;
        }
        
        // 验证维度参数
        if (dimensions != null && dimensions <= 0) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取输入文本数量
     * 
     * 返回请求中包含的文本数量，用于批量处理控制。
     * 
     * @return 文本数量
     */
    public int getInputCount() {
        if (input instanceof String) {
            return 1;
        } else if (input instanceof List) {
            return ((List<?>) input).size();
        }
        return 0;
    }

    /**
     * 检查是否为批量请求
     * 
     * @return 是否为批量请求
     */
    public boolean isBatchRequest() {
        return input instanceof List;
    }
}
