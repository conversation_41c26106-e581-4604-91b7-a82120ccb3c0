package cn.iflytek.imagesearch.infrastructure.embedding.config;

import cn.iflytek.imagesearch.infrastructure.embedding.client.AliyunEmbeddingClient;
import cn.iflytek.imagesearch.infrastructure.embedding.service.AliyunEmbeddingServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 嵌入服务自动配置类
 * 
 * 负责根据配置条件自动创建和配置嵌入服务相关的Bean。
 * 该配置类确保只有在启用阿里云嵌入服务时才创建相关组件，
 * 避免不必要的资源消耗和依赖问题。
 * 
 * 配置条件：
 * - image-search.embedding.aliyun.enabled=true
 * - 有效的API密钥和端点配置
 * 
 * 创建的Bean：
 * - AliyunEmbeddingClient: HTTP客户端
 * - AliyunEmbeddingServiceImpl: 服务实现
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(AliyunEmbeddingConfig.class)
@ConditionalOnProperty(
    prefix = "image-search.embedding.aliyun", 
    name = "enabled", 
    havingValue = "true"
)
public class EmbeddingAutoConfiguration {

    /**
     * 创建阿里云嵌入HTTP客户端Bean
     * 
     * @param config 阿里云嵌入服务配置
     * @return HTTP客户端实例
     */
    @Bean
    @ConditionalOnProperty(
        prefix = "image-search.embedding.aliyun", 
        name = "enabled", 
        havingValue = "true"
    )
    public AliyunEmbeddingClient aliyunEmbeddingClient(AliyunEmbeddingConfig config) {
        log.info("创建阿里云嵌入HTTP客户端，端点: {}", config.getBaseUrl());
        return new AliyunEmbeddingClient(config);
    }

    /**
     * 创建阿里云嵌入服务实现Bean
     * 
     * @param config 阿里云嵌入服务配置
     * @param client HTTP客户端
     * @return 服务实现实例
     */
    @Bean
    @ConditionalOnProperty(
        prefix = "image-search.embedding.aliyun", 
        name = "enabled", 
        havingValue = "true"
    )
    public AliyunEmbeddingServiceImpl aliyunEmbeddingService(
            AliyunEmbeddingConfig config, 
            AliyunEmbeddingClient client) {
        log.info("创建阿里云嵌入服务实现，模型: {}", config.getModelName());
        return new AliyunEmbeddingServiceImpl(config, client);
    }
}
