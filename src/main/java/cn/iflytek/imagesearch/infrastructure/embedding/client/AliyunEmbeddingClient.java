package cn.iflytek.imagesearch.infrastructure.embedding.client;

import cn.iflytek.imagesearch.infrastructure.embedding.config.AliyunEmbeddingConfig;
import cn.iflytek.imagesearch.infrastructure.embedding.dto.EmbeddingRequest;
import cn.iflytek.imagesearch.infrastructure.embedding.dto.EmbeddingResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 阿里云百炼嵌入服务HTTP客户端
 * 
 * 负责与阿里云百炼嵌入API进行HTTP通信，封装了请求发送、响应处理、
 * 错误重试、日志记录等基础设施功能。使用OkHttp作为底层HTTP客户端，
 * 提供高性能和可靠的网络通信能力。
 * 
 * 主要功能：
 * 1. HTTP请求发送和响应处理
 * 2. 自动重试机制
 * 3. 请求和响应日志记录
 * 4. 连接池管理和超时控制
 * 5. 错误处理和异常转换
 * 
 * 技术特点：
 * - 基于OkHttp的高性能HTTP客户端
 * - 支持连接复用和请求管道化
 * - 自动处理GZIP压缩
 * - 完整的SSL/TLS支持
 * - 可配置的超时和重试策略
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
@Slf4j
@Component
public class AliyunEmbeddingClient {

    private static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String CONTENT_TYPE_HEADER = "Content-Type";
    private static final String USER_AGENT_HEADER = "User-Agent";
    private static final String USER_AGENT_VALUE = "ImageSearch-EmbeddingClient/1.0";

    private final AliyunEmbeddingConfig config;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;

    /**
     * 构造函数
     * 
     * 初始化HTTP客户端和相关组件，配置超时、重试、日志等参数。
     * 
     * @param config 阿里云嵌入服务配置
     */
    public AliyunEmbeddingClient(AliyunEmbeddingConfig config) {
        this.config = config;
        this.objectMapper = new ObjectMapper();
        this.httpClient = createHttpClient();
        
        log.info("阿里云嵌入客户端初始化完成，端点: {}, 模型: {}", 
                config.getBaseUrl(), config.getModelName());
    }

    /**
     * 发送嵌入请求
     * 
     * 向阿里云百炼API发送嵌入请求，获取文本的向量表示。
     * 包含完整的错误处理和重试逻辑。
     * 
     * @param request 嵌入请求对象
     * @return 嵌入响应对象
     * @throws EmbeddingClientException 当请求失败时抛出
     */
    public EmbeddingResponse sendEmbeddingRequest(EmbeddingRequest request) throws EmbeddingClientException {
        if (!request.isValid()) {
            throw new EmbeddingClientException("请求参数无效: " + request);
        }

        int retryCount = 0;
        Exception lastException = null;

        while (retryCount <= config.getMaxRetries()) {
            try {
                return executeRequest(request);
            } catch (Exception e) {
                lastException = e;
                retryCount++;
                
                if (retryCount <= config.getMaxRetries() && isRetryableException(e)) {
                    log.warn("嵌入请求失败，准备重试 ({}/{}): {}", 
                            retryCount, config.getMaxRetries(), e.getMessage());
                    
                    try {
                        Thread.sleep(config.getRetryIntervalMs());
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new EmbeddingClientException("请求被中断", ie);
                    }
                } else {
                    break;
                }
            }
        }

        throw new EmbeddingClientException("嵌入请求最终失败，重试次数: " + retryCount, lastException);
    }

    /**
     * 执行单次HTTP请求
     * 
     * @param request 嵌入请求对象
     * @return 嵌入响应对象
     * @throws Exception 当请求失败时抛出
     */
    private EmbeddingResponse executeRequest(EmbeddingRequest request) throws Exception {
        // 构建HTTP请求
        String requestBody = objectMapper.writeValueAsString(request);
        Request httpRequest = new Request.Builder()
                .url(config.getEmbeddingEndpoint())
                .addHeader(AUTHORIZATION_HEADER, "Bearer " + config.getApiKey())
                .addHeader(CONTENT_TYPE_HEADER, "application/json")
                .addHeader(USER_AGENT_HEADER, USER_AGENT_VALUE)
                .post(RequestBody.create(requestBody, JSON_MEDIA_TYPE))
                .build();

        if (config.isEnableRequestLogging()) {
            log.debug("发送嵌入请求: {}", requestBody);
        }

        // 发送请求并处理响应
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body() != null ? response.body().string() : "";
            
            if (config.isEnableResponseLogging()) {
                log.debug("收到嵌入响应: 状态码={}, 响应体={}", response.code(), responseBody);
            }

            if (!response.isSuccessful()) {
                handleErrorResponse(response.code(), responseBody);
            }

            EmbeddingResponse embeddingResponse = objectMapper.readValue(responseBody, EmbeddingResponse.class);
            
            if (!embeddingResponse.isValid()) {
                throw new EmbeddingClientException("响应数据无效: " + responseBody);
            }

            log.debug("嵌入请求成功，返回{}个向量，维度: {}", 
                    embeddingResponse.getEmbeddingCount(), embeddingResponse.getEmbeddingDimension());
            
            return embeddingResponse;
        }
    }

    /**
     * 处理错误响应
     * 
     * @param statusCode HTTP状态码
     * @param responseBody 响应体内容
     * @throws EmbeddingClientException 抛出具体的错误异常
     */
    private void handleErrorResponse(int statusCode, String responseBody) throws EmbeddingClientException {
        String errorMessage = "HTTP " + statusCode + ": " + responseBody;
        
        switch (statusCode) {
            case 400:
                throw new EmbeddingClientException("请求参数错误: " + responseBody);
            case 401:
                throw new EmbeddingClientException("API密钥无效或已过期: " + responseBody);
            case 403:
                throw new EmbeddingClientException("访问被拒绝，请检查权限: " + responseBody);
            case 404:
                throw new EmbeddingClientException("API端点不存在: " + responseBody);
            case 429:
                throw new EmbeddingClientException("请求频率超限，请稍后重试: " + responseBody);
            case 500:
                throw new EmbeddingClientException("服务器内部错误: " + responseBody);
            case 502:
            case 503:
            case 504:
                throw new EmbeddingClientException("服务暂时不可用: " + responseBody);
            default:
                throw new EmbeddingClientException(errorMessage);
        }
    }

    /**
     * 判断异常是否可重试
     * 
     * @param exception 异常对象
     * @return 是否可重试
     */
    private boolean isRetryableException(Exception exception) {
        if (exception instanceof IOException) {
            return true; // 网络异常可重试
        }
        
        if (exception instanceof EmbeddingClientException) {
            String message = exception.getMessage();
            // 服务器错误和限流错误可重试
            return message.contains("500") || message.contains("502") || 
                   message.contains("503") || message.contains("504") || 
                   message.contains("429");
        }
        
        return false;
    }

    /**
     * 创建HTTP客户端
     * 
     * @return 配置好的OkHttpClient实例
     */
    private OkHttpClient createHttpClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(Duration.ofMillis(config.getTimeoutMs()))
                .readTimeout(Duration.ofMillis(config.getTimeoutMs()))
                .writeTimeout(Duration.ofMillis(config.getTimeoutMs()))
                .retryOnConnectionFailure(false) // 禁用自动重试，使用自定义重试逻辑
                .connectionPool(new ConnectionPool(5, 5, TimeUnit.MINUTES));

        // 添加日志拦截器
        if (config.isEnableRequestLogging() || config.isEnableResponseLogging()) {
            HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(log::debug);
            
            if (config.isEnableRequestLogging() && config.isEnableResponseLogging()) {
                loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
            } else if (config.isEnableRequestLogging()) {
                loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.HEADERS);
            } else {
                loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BASIC);
            }
            
            builder.addInterceptor(loggingInterceptor);
        }

        return builder.build();
    }

    /**
     * 测试连接可用性
     * 
     * 发送一个简单的测试请求来验证API连接是否正常。
     * 
     * @return 连接是否可用
     */
    public boolean testConnection() {
        try {
            EmbeddingRequest testRequest = EmbeddingRequest.createSingleTextRequest(
                    config.getModelName(), "test", null);
            
            EmbeddingResponse response = sendEmbeddingRequest(testRequest);
            return response.isValid();
            
        } catch (Exception e) {
            log.warn("连接测试失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 嵌入客户端异常类
     */
    public static class EmbeddingClientException extends Exception {
        public EmbeddingClientException(String message) {
            super(message);
        }

        public EmbeddingClientException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
