package cn.iflytek.imagesearch.infrastructure.embedding.service;

import cn.iflytek.imagesearch.domain.service.embedding.EmbeddingService;
import cn.iflytek.imagesearch.infrastructure.embedding.client.AliyunEmbeddingClient;
import cn.iflytek.imagesearch.infrastructure.embedding.config.AliyunEmbeddingConfig;
import cn.iflytek.imagesearch.infrastructure.embedding.dto.EmbeddingRequest;
import cn.iflytek.imagesearch.infrastructure.embedding.dto.EmbeddingResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 阿里云百炼嵌入服务实现类（防腐层）
 * 
 * 实现领域层的EmbeddingService接口，封装对阿里云百炼嵌入API的调用。
 * 该类作为防腐层，隔离外部API的变化对领域层的影响，提供稳定的
 * 领域服务接口实现。
 * 
 * 防腐层职责：
 * 1. 适配外部API格式到领域接口
 * 2. 处理外部服务的异常和错误
 * 3. 实现领域层定义的业务逻辑
 * 4. 提供服务降级和回退机制
 * 5. 统一日志记录和监控
 * 
 * 技术特点：
 * - 条件化Bean创建，仅在配置启用时生效
 * - 完整的错误处理和异常转换
 * - 批量处理优化，自动分批调用
 * - 参数验证和边界检查
 * - 详细的日志记录和性能监控
 * 
 * 配置要求：
 * - image-search.embedding.aliyun.enabled=true
 * - 有效的API密钥和端点配置
 * - 合理的超时和重试参数设置
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
@Slf4j
@Service
@ConditionalOnProperty(
    prefix = "image-search.embedding.aliyun", 
    name = "enabled", 
    havingValue = "true"
)
public class AliyunEmbeddingServiceImpl implements EmbeddingService {

    private final AliyunEmbeddingConfig config;
    private final AliyunEmbeddingClient client;

    /**
     * 构造函数
     * 
     * @param config 阿里云嵌入服务配置
     * @param client 阿里云嵌入HTTP客户端
     */
    public AliyunEmbeddingServiceImpl(AliyunEmbeddingConfig config, AliyunEmbeddingClient client) {
        this.config = config;
        this.client = client;
        
        // 验证配置有效性
        if (!config.isValid()) {
            log.error("阿里云嵌入服务配置无效: {}", config);
            throw new IllegalArgumentException("阿里云嵌入服务配置无效");
        }
        
        log.info("阿里云嵌入服务初始化完成，模型: {}, 默认维度: {}", 
                config.getModelName(), config.getDefaultDimension());
    }

    @Override
    public float[] convertTextToVector(String text) {
        return convertTextToVector(text, config.getDefaultDimension());
    }

    @Override
    public float[] convertTextToVector(String text, int dimension) {
        // 参数验证
        if (text == null || text.trim().isEmpty()) {
            log.warn("输入文本为空，返回null");
            return null;
        }
        
        if (!config.getSupportedDimensions().contains(dimension)) {
            throw new IllegalArgumentException("不支持的向量维度: " + dimension + 
                    ", 支持的维度: " + config.getSupportedDimensions());
        }

        log.debug("使用真实嵌入服务转换文本: {}", text.length() > 50 ? text.substring(0, 50) + "..." : text);

        try {
            // 创建请求
            EmbeddingRequest request = EmbeddingRequest.createSingleTextRequest(
                    config.getModelName(), text, dimension);
            
            // 发送请求
            EmbeddingResponse response = client.sendEmbeddingRequest(request);
            
            // 提取向量数据
            float[] vector = response.getFirstEmbedding();
            if (vector != null) {
                log.debug("真实嵌入服务转换成功，向量维度: {}", vector.length);
            } else {
                log.warn("真实嵌入服务返回空向量");
            }
            
            return vector;
            
        } catch (Exception e) {
            log.error("真实嵌入服务调用失败: {}", e.getMessage(), e);
            throw new RuntimeException("文本向量转换失败", e);
        }
    }

    @Override
    public List<float[]> convertTextsToVectors(List<String> texts) {
        return convertTextsToVectors(texts, config.getDefaultDimension());
    }

    @Override
    public List<float[]> convertTextsToVectors(List<String> texts, int dimension) {
        // 参数验证
        if (texts == null || texts.isEmpty()) {
            log.warn("输入文本列表为空，返回空列表");
            return new ArrayList<>();
        }
        
        if (!config.getSupportedDimensions().contains(dimension)) {
            throw new IllegalArgumentException("不支持的向量维度: " + dimension + 
                    ", 支持的维度: " + config.getSupportedDimensions());
        }

        // 过滤无效文本
        List<String> validTexts = texts.stream()
                .filter(text -> text != null && !text.trim().isEmpty())
                .toList();
        
        if (validTexts.isEmpty()) {
            log.warn("没有有效的输入文本，返回空列表");
            return new ArrayList<>();
        }

        log.debug("使用真实嵌入服务批量转换文本，数量: {}", validTexts.size());

        try {
            List<float[]> allVectors = new ArrayList<>();
            int batchSize = Math.min(config.getBatchSize(), config.getMaxBatchSize());
            
            // 分批处理
            for (int i = 0; i < validTexts.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, validTexts.size());
                List<String> batch = validTexts.subList(i, endIndex);
                
                log.debug("处理批次 {}-{}/{}", i + 1, endIndex, validTexts.size());
                
                // 创建批量请求
                EmbeddingRequest request = EmbeddingRequest.createBatchTextRequest(
                        config.getModelName(), batch, dimension);
                
                // 发送请求
                EmbeddingResponse response = client.sendEmbeddingRequest(request);
                
                // 提取向量数据
                List<float[]> batchVectors = response.getAllEmbeddings();
                allVectors.addAll(batchVectors);
                
                log.debug("批次处理完成，获得{}个向量", batchVectors.size());
            }
            
            log.debug("真实嵌入服务批量转换完成，总向量数: {}", allVectors.size());
            return allVectors;
            
        } catch (Exception e) {
            log.error("真实嵌入服务批量调用失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量文本向量转换失败", e);
        }
    }

    @Override
    public boolean isServiceAvailable() {
        try {
            log.debug("检查阿里云嵌入服务可用性");
            boolean available = client.testConnection();
            log.debug("阿里云嵌入服务可用性: {}", available);
            return available;
        } catch (Exception e) {
            log.warn("阿里云嵌入服务可用性检查失败: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public List<Integer> getSupportedDimensions() {
        return config.getSupportedDimensions();
    }

    @Override
    public int getDefaultDimension() {
        return config.getDefaultDimension();
    }

    /**
     * 获取服务配置信息
     * 
     * 用于监控和调试，返回当前服务的关键配置参数。
     * 
     * @return 配置信息字符串
     */
    public String getServiceInfo() {
        return String.format("AliyunEmbeddingService[model=%s, dimension=%d, endpoint=%s, batchSize=%d]",
                config.getModelName(), 
                config.getDefaultDimension(),
                config.getBaseUrl(),
                config.getBatchSize());
    }

    /**
     * 获取服务统计信息
     * 
     * 返回服务的运行状态和统计信息，用于监控和运维。
     * 
     * @return 统计信息
     */
    public ServiceStats getServiceStats() {
        return ServiceStats.builder()
                .serviceName("AliyunEmbeddingService")
                .modelName(config.getModelName())
                .defaultDimension(config.getDefaultDimension())
                .supportedDimensions(config.getSupportedDimensions())
                .batchSize(config.getBatchSize())
                .maxRetries(config.getMaxRetries())
                .timeoutMs(config.getTimeoutMs())
                .available(isServiceAvailable())
                .build();
    }

    /**
     * 服务统计信息
     */
    public static class ServiceStats {
        private String serviceName;
        private String modelName;
        private int defaultDimension;
        private List<Integer> supportedDimensions;
        private int batchSize;
        private int maxRetries;
        private long timeoutMs;
        private boolean available;

        // Builder pattern
        public static ServiceStatsBuilder builder() {
            return new ServiceStatsBuilder();
        }

        public static class ServiceStatsBuilder {
            private ServiceStats stats = new ServiceStats();

            public ServiceStatsBuilder serviceName(String serviceName) {
                stats.serviceName = serviceName;
                return this;
            }

            public ServiceStatsBuilder modelName(String modelName) {
                stats.modelName = modelName;
                return this;
            }

            public ServiceStatsBuilder defaultDimension(int defaultDimension) {
                stats.defaultDimension = defaultDimension;
                return this;
            }

            public ServiceStatsBuilder supportedDimensions(List<Integer> supportedDimensions) {
                stats.supportedDimensions = supportedDimensions;
                return this;
            }

            public ServiceStatsBuilder batchSize(int batchSize) {
                stats.batchSize = batchSize;
                return this;
            }

            public ServiceStatsBuilder maxRetries(int maxRetries) {
                stats.maxRetries = maxRetries;
                return this;
            }

            public ServiceStatsBuilder timeoutMs(long timeoutMs) {
                stats.timeoutMs = timeoutMs;
                return this;
            }

            public ServiceStatsBuilder available(boolean available) {
                stats.available = available;
                return this;
            }

            public ServiceStats build() {
                return stats;
            }
        }

        // Getters
        public String getServiceName() { return serviceName; }
        public String getModelName() { return modelName; }
        public int getDefaultDimension() { return defaultDimension; }
        public List<Integer> getSupportedDimensions() { return supportedDimensions; }
        public int getBatchSize() { return batchSize; }
        public int getMaxRetries() { return maxRetries; }
        public long getTimeoutMs() { return timeoutMs; }
        public boolean isAvailable() { return available; }
    }
}
