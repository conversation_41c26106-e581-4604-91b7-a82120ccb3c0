package cn.iflytek.imagesearch.infrastructure.embedding.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 阿里云百炼嵌入API响应DTO
 * 
 * 封装阿里云百炼嵌入服务返回的响应数据，支持OpenAI兼容格式。
 * 该DTO包含向量数据、使用统计和请求元信息。
 * 
 * 响应格式示例：
 * ```json
 * {
 *   "object": "list",
 *   "data": [
 *     {
 *       "object": "embedding",
 *       "index": 0,
 *       "embedding": [0.1, 0.2, 0.3, ...]
 *     }
 *   ],
 *   "model": "text-embedding-v4",
 *   "usage": {
 *     "prompt_tokens": 10,
 *     "total_tokens": 10
 *   },
 *   "id": "req_123456"
 * }
 * ```
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmbeddingResponse {

    /**
     * 响应对象类型
     * 
     * 固定值"list"，表示这是一个包含多个嵌入向量的列表响应。
     * 符合OpenAI API规范。
     */
    @JsonProperty("object")
    private String object;

    /**
     * 嵌入向量数据列表
     * 
     * 包含所有输入文本对应的向量数据。列表顺序与输入文本顺序一致。
     * 每个元素包含向量数据和索引信息。
     */
    @JsonProperty("data")
    private List<EmbeddingData> data;

    /**
     * 使用的模型名称
     * 
     * 返回实际使用的嵌入模型名称，与请求中的model字段对应。
     * 用于确认API使用了正确的模型。
     */
    @JsonProperty("model")
    private String model;

    /**
     * 使用统计信息
     * 
     * 包含本次请求的token使用量统计，用于计费和监控。
     */
    @JsonProperty("usage")
    private Usage usage;

    /**
     * 请求ID
     * 
     * 唯一标识本次API请求的ID，用于问题追踪和日志关联。
     * 在遇到问题时可以提供给技术支持进行排查。
     */
    @JsonProperty("id")
    private String id;

    /**
     * 嵌入向量数据
     * 
     * 表示单个文本对应的向量数据和元信息。
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmbeddingData {

        /**
         * 数据对象类型
         * 
         * 固定值"embedding"，表示这是一个嵌入向量对象。
         */
        @JsonProperty("object")
        private String object;

        /**
         * 向量索引
         * 
         * 该向量在输入文本列表中的索引位置（从0开始）。
         * 用于将向量与对应的输入文本进行匹配。
         */
        @JsonProperty("index")
        private int index;

        /**
         * 向量数据
         * 
         * 文本的数值向量表示，通常为浮点数数组。
         * 向量维度由请求参数或模型默认设置决定。
         * 
         * 数据特点：
         * - 元素类型：float
         * - 数值范围：通常在[-1, 1]之间
         * - 维度：64-2048不等，取决于模型和配置
         */
        @JsonProperty("embedding")
        private List<Float> embedding;

        /**
         * 将向量数据转换为float数组
         * 
         * 便捷方法，将List<Float>转换为float[]数组格式，
         * 便于后续的数值计算和存储。
         * 
         * @return float数组格式的向量
         */
        public float[] getEmbeddingArray() {
            if (embedding == null || embedding.isEmpty()) {
                return new float[0];
            }
            
            float[] array = new float[embedding.size()];
            for (int i = 0; i < embedding.size(); i++) {
                Float value = embedding.get(i);
                array[i] = value != null ? value : 0.0f;
            }
            return array;
        }

        /**
         * 获取向量维度
         * 
         * @return 向量的维度大小
         */
        public int getDimension() {
            return embedding != null ? embedding.size() : 0;
        }
    }

    /**
     * 使用统计信息
     * 
     * 记录API调用的资源使用情况，主要用于计费和监控。
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Usage {

        /**
         * 提示词token数量
         * 
         * 输入文本转换为token后的总数量。token是模型处理的基本单位，
         * 通常一个汉字对应1-2个token，英文单词对应1个或多个token。
         */
        @JsonProperty("prompt_tokens")
        private int promptTokens;

        /**
         * 总token数量
         * 
         * 本次请求消耗的总token数量。对于嵌入API，
         * 通常等于prompt_tokens，因为没有生成新的文本。
         */
        @JsonProperty("total_tokens")
        private int totalTokens;
    }

    /**
     * 检查响应是否有效
     * 
     * 验证响应数据的完整性和有效性。
     * 
     * @return 响应是否有效
     */
    public boolean isValid() {
        return data != null && !data.isEmpty() && 
               model != null && !model.trim().isEmpty();
    }

    /**
     * 获取第一个向量数据
     * 
     * 便捷方法，用于单文本请求时快速获取向量结果。
     * 
     * @return 第一个向量的float数组，如果没有数据则返回null
     */
    public float[] getFirstEmbedding() {
        if (data == null || data.isEmpty()) {
            return null;
        }
        return data.get(0).getEmbeddingArray();
    }

    /**
     * 获取所有向量数据
     * 
     * 便捷方法，将所有向量数据转换为float数组列表。
     * 
     * @return 所有向量的float数组列表
     */
    public List<float[]> getAllEmbeddings() {
        if (data == null) {
            return List.of();
        }
        
        return data.stream()
                .map(EmbeddingData::getEmbeddingArray)
                .toList();
    }

    /**
     * 获取向量数量
     * 
     * @return 响应中包含的向量数量
     */
    public int getEmbeddingCount() {
        return data != null ? data.size() : 0;
    }

    /**
     * 获取向量维度
     * 
     * 返回第一个向量的维度，假设所有向量维度相同。
     * 
     * @return 向量维度，如果没有数据则返回0
     */
    public int getEmbeddingDimension() {
        if (data == null || data.isEmpty()) {
            return 0;
        }
        return data.get(0).getDimension();
    }

    /**
     * 获取token使用量
     * 
     * @return token使用量，如果没有统计信息则返回0
     */
    public int getTotalTokens() {
        return usage != null ? usage.getTotalTokens() : 0;
    }
}
